//
//  LotteryDetailView.swift
//  LotteryLightbox
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 6/30/25.
//

import SwiftUI

struct LotteryDetailView: View {
    let lotteryType: LotteryType
    let onRefresh: () -> Void
    
    @StateObject private var viewModel = LotteryDetailViewModel()
    
    var body: some View {
        VStack(spacing: 30) {
            // Header
            LotteryHeaderView(lotteryType: lotteryType)
            
            // Main Content
            if viewModel.isLoading {
                LoadingView()
            } else if let error = viewModel.errorMessage {
                ErrorView(error: error, onRetry: {
                    loadData()
                })
            } else if let info = viewModel.drawingInfo {
                LotteryInfoView(drawingInfo: info)
            } else {
                EmptyStateView(onLoadData: {
                    loadData()
                })
            }
            
            Spacer()
        }
        .padding()
        .navigationTitle("Lottery Lightbox")
        #if os(iOS)
        .navigationBarTitleDisplayMode(.inline)
        #endif
        .toolbar {
            ToolbarItem(placement: .primaryAction) {
                Button("Refresh") {
                    loadData()
                }
                .disabled(viewModel.isLoading)
            }
        }
        .onAppear {
            loadData()
        }
    }
    
    private func loadData() {
        onRefresh()
        viewModel.fetchDrawingInfo(for: lotteryType)
    }
}

// MARK: - Header View
struct LotteryHeaderView: View {
    let lotteryType: LotteryType
    
    var body: some View {
        VStack(spacing: 10) {
            Image(systemName: lotteryType.icon)
                .font(.system(size: 60))
                .foregroundStyle(lotteryType.colors[0], lotteryType.colors[1])
            
            Text(lotteryType.rawValue)
                .font(.largeTitle)
                .fontWeight(.bold)
                .foregroundColor(.primary)
        }
    }
}

// MARK: - Loading View
struct LoadingView: View {
    var body: some View {
        VStack(spacing: 20) {
            ProgressView()
                .scaleEffect(1.5)
            Text("Loading lottery information...")
                .font(.headline)
                .foregroundColor(.secondary)
        }
        .frame(maxHeight: .infinity)
    }
}

// MARK: - Error View
struct ErrorView: View {
    let error: String
    let onRetry: () -> Void
    
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "exclamationmark.triangle")
                .font(.system(size: 50))
                .foregroundColor(.orange)
            
            Text("Unable to load lottery data")
                .font(.headline)
            
            Text(error)
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            Button("Try Again") {
                onRetry()
            }
            .buttonStyle(.borderedProminent)
        }
        .frame(maxHeight: .infinity)
    }
}

// MARK: - Empty State View
struct EmptyStateView: View {
    let onLoadData: () -> Void
    
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "questionmark.circle")
                .font(.system(size: 50))
                .foregroundColor(.gray)
            
            Text("No lottery data available")
                .font(.headline)
                .foregroundColor(.secondary)
            
            Button("Load Data") {
                onLoadData()
            }
            .buttonStyle(.borderedProminent)
        }
        .frame(maxHeight: .infinity)
    }
}
