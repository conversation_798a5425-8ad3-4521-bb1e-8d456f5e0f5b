//
//  SidebarView.swift
//  LotteryLightbox
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 6/30/25.
//

import SwiftUI

struct SidebarView: View {
    let selectedLottery: LotteryType
    let onLotterySelected: (LotteryType) -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 20) {
            Text("Lottery Games")
                .font(.headline)
                .padding(.horizontal)
            
            ForEach(LotteryType.allCases, id: \.self) { lottery in
                #if os(iOS)
                NavigationLink(
                    destination: LotteryDetailView(
                        lotteryType: lottery,
                        onRefresh: {
                            // Refresh handled by detail view
                        }
                    )
                ) {
                    SidebarItemView(
                        lottery: lottery,
                        isSelected: selectedLottery == lottery,
                        onTap: {
                            onLotterySelected(lottery)
                        }
                    )
                }
                #else
                SidebarItemView(
                    lottery: lottery,
                    isSelected: selectedLottery == lottery,
                    onTap: {
                        onLotterySelected(lottery)
                    }
                )
                #endif
            }
            
            Spacer()
        }
        .frame(minWidth: 200)
        .navigationTitle("Menu")
    }
}

#Preview("Sidebar - macOS") {
    NavigationView {
        SidebarView(
            selectedLottery: .powerball,
            onLotterySelected: { _ in }
        )

        Text("Select a lottery from the sidebar")
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .background(Color.gray.opacity(0.1))
    }
    .navigationViewStyle(DoubleColumnNavigationViewStyle())
    .frame(width: 800, height: 600)
}

#Preview("Sidebar - iOS") {
    NavigationView {
        SidebarView(
            selectedLottery: .megaMillions,
            onLotterySelected: { _ in }
        )
    }
    #if os(iOS)
    .navigationViewStyle(StackNavigationViewStyle())
    #endif
}
