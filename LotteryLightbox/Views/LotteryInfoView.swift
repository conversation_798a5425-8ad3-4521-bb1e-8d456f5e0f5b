//
//  LotteryInfoView.swift
//  LotteryLightbox
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 6/30/25.
//

import SwiftUI

struct LotteryInfoView: View {
    let drawingInfo: DrawingInfo
    
    var body: some View {
        VStack(spacing: 25) {
            // Next Drawing Date
            VStack(spacing: 8) {
                Text("Next Drawing")
                    .font(.headline)
                    .foregroundColor(.secondary)
                
                Text(drawingInfo.date, style: .date)
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Text(drawingInfo.date, style: .time)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            .padding()
            .background(Color.gray.opacity(0.1))
            .cornerRadius(12)
            
            // Jackpot Information
            VStack(spacing: 20) {
                // Estimated Jackpot
                VStack(spacing: 8) {
                    Text("Estimated Jackpot")
                        .font(.headline)
                        .foregroundColor(.secondary)
                    
                    Text(drawingInfo.estimatedJackpot)
                        .font(.system(size: 36, weight: .bold, design: .rounded))
                        .foregroundColor(.primary)
                        .multilineTextAlignment(.center)
                }
                .padding()
                .frame(maxWidth: .infinity)
                .background(
                    LinearGradient(
                        colors: [Color.blue.opacity(0.1), Color.purple.opacity(0.1)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .cornerRadius(16)
                
                // Cash Value
                VStack(spacing: 8) {
                    Text("Cash Value")
                        .font(.headline)
                        .foregroundColor(.secondary)
                    
                    Text(drawingInfo.cashValue)
                        .font(.system(size: 28, weight: .semibold, design: .rounded))
                        .foregroundColor(.primary)
                        .multilineTextAlignment(.center)
                }
                .padding()
                .frame(maxWidth: .infinity)
                .background(
                    LinearGradient(
                        colors: [Color.green.opacity(0.1), Color.mint.opacity(0.1)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .cornerRadius(16)
            }
            
            // Disclaimer
            Text("Lottery numbers are drawn randomly. Please play responsibly.")
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.top)
        }
        .padding()
    }
}

#Preview("Lottery Info - Powerball") {
    LotteryInfoView(
        drawingInfo: DrawingInfo(
            date: Date().addingTimeInterval(86400 * 3), // 3 days from now
            estimatedJackpot: "$1.2 Billion",
            cashValue: "$551.7 Million"
        )
    )
    .frame(maxWidth: 400)
}

#Preview("Lottery Info - Mega Millions") {
    LotteryInfoView(
        drawingInfo: DrawingInfo(
            date: Date().addingTimeInterval(86400 * 2), // 2 days from now
            estimatedJackpot: "$875 Million",
            cashValue: "$413.5 Million"
        )
    )
    .frame(maxWidth: 400)
}

#Preview("Lottery Info - Small Jackpot") {
    LotteryInfoView(
        drawingInfo: DrawingInfo(
            date: Date().addingTimeInterval(86400), // Tomorrow
            estimatedJackpot: "$20 Million",
            cashValue: "$9.8 Million"
        )
    )
    .frame(maxWidth: 400)
}
