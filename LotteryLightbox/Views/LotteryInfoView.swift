//
//  LotteryInfoView.swift
//  LotteryLightbox
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 6/30/25.
//

import SwiftUI

struct LotteryInfoView: View {
    let drawingInfo: DrawingInfo
    
    var body: some View {
        VStack(spacing: 25) {
            // Next Drawing Date
            VStack(spacing: 8) {
                Text("Next Drawing")
                    .font(.headline)
                    .foregroundColor(.secondary)
                
                Text(drawingInfo.date, style: .date)
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Text(drawingInfo.date, style: .time)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            .padding()
            .background(Color.gray.opacity(0.1))
            .cornerRadius(12)
            
            // Jackpot Information
            VStack(spacing: 20) {
                // Estimated Jackpot
                VStack(spacing: 8) {
                    Text("Estimated Jackpot")
                        .font(.headline)
                        .foregroundColor(.secondary)
                    
                    Text(drawingInfo.estimatedJackpot)
                        .font(.system(size: 36, weight: .bold, design: .rounded))
                        .foregroundColor(.primary)
                        .multilineTextAlignment(.center)
                }
                .padding()
                .frame(maxWidth: .infinity)
                .background(
                    LinearGradient(
                        colors: [Color.blue.opacity(0.1), Color.purple.opacity(0.1)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .cornerRadius(16)
                
                // Cash Value
                VStack(spacing: 8) {
                    Text("Cash Value")
                        .font(.headline)
                        .foregroundColor(.secondary)
                    
                    Text(drawingInfo.cashValue)
                        .font(.system(size: 28, weight: .semibold, design: .rounded))
                        .foregroundColor(.primary)
                        .multilineTextAlignment(.center)
                }
                .padding()
                .frame(maxWidth: .infinity)
                .background(
                    LinearGradient(
                        colors: [Color.green.opacity(0.1), Color.mint.opacity(0.1)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .cornerRadius(16)
            }
            
            // Disclaimer
            Text("Lottery numbers are drawn randomly. Please play responsibly.")
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.top)
        }
        .padding()
    }
}
