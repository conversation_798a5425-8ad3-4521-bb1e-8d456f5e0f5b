//
//  ContentView.swift
//  LotteryLightbox
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 6/30/25.
//

import SwiftUI

struct ContentView: View {
    @State private var selectedLottery: LotteryType = .powerball

    var body: some View {
        NavigationView {
            // Sidebar
            SidebarView(
                selectedLottery: selectedLottery,
                onLotterySelected: { lottery in
                    selectedLottery = lottery
                }
            )

            // Main content (right panel) - Default view for macOS
            LotteryDetailView(
                lotteryType: selectedLottery,
                onRefresh: {
                    // Refresh action handled by the detail view
                }
            )
        }
        .navigationViewStyle(DoubleColumnNavigationViewStyle())
    }
}

#Preview("ContentView - macOS") {
    ContentView()
        .frame(width: 1000, height: 700)
}

#Preview("ContentView - iOS") {
    ContentView()
        .previewDevice("iPhone 15 Pro")
}

#Preview("ContentView - iPad") {
    ContentView()
        .previewDevice("iPad Pro (12.9-inch) (6th generation)")
}
