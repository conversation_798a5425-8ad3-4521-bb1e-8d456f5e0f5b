//
//  ContentView.swift
//  LotteryLightbox
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 6/30/25.
//

import SwiftUI

struct ContentView: View {
    @State private var selectedLottery: LotteryType = .powerball

    var body: some View {
        NavigationView {
            // Sidebar
            SidebarView(
                selectedLottery: selectedLottery,
                onLotterySelected: { lottery in
                    selectedLottery = lottery
                }
            )

            // Main content (right panel) - Default view for macOS
            LotteryDetailView(
                lotteryType: selectedLottery,
                onRefresh: {
                    // Refresh action handled by the detail view
                }
            )
        }
        .navigationViewStyle(DoubleColumnNavigationViewStyle())
    }
}

#Preview {
    ContentView()
}
