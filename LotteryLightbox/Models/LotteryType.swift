//
//  LotteryType.swift
//  LotteryLightbox
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 6/30/25.
//

import SwiftUI

enum LotteryType: String, CaseIterable {
    case powerball = "Powerball"
    case megaMillions = "Mega Millions"
    
    var icon: String {
        switch self {
        case .powerball:
            return "circle.fill"
        case .megaMillions:
            return "star.fill"
        }
    }
    
    var colors: [Color] {
        switch self {
        case .powerball:
            return [.red, .white]
        case .megaMillions:
            return [.yellow, .blue]
        }
    }
}
