//
//  LotteryType.swift
//  LotteryLightbox
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 6/30/25.
//

import SwiftUI

enum LotteryType: String, CaseIterable {
    case powerball = "Powerball"
    case megaMillions = "Mega Millions"

    var icon: String {
        switch self {
        case .powerball:
            return "circle.fill"
        case .megaMillions:
            return "star.fill"
        }
    }

    var colors: [Color] {
        switch self {
        case .powerball:
            return [.red, .white]
        case .megaMillions:
            return [.yellow, .blue]
        }
    }
}

#Preview("Lottery Type Icons") {
    VStack(spacing: 20) {
        ForEach(LotteryType.allCases, id: \.self) { lottery in
            HStack {
                Image(systemName: lottery.icon)
                    .foregroundStyle(lottery.colors[0], lottery.colors[1])
                    .font(.title)

                Text(lottery.rawValue)
                    .font(.headline)
            }
        }
    }
    .padding()
}
