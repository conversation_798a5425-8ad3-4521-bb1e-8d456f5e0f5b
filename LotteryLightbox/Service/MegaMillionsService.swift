//
//  MegaMillionsService.swift
//  LotteryLightbox
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 7/1/25.
//

import Foundation

class MegaMillionsService: LotterServiceProtocol {
    var response: String?
    
    init(response: String? = nil) {
        self.response = response
    }
    
    func fetchNextDrawingInfo() async throws -> DrawingInfo {
        if response == nil {
            guard let url = URL(string: "https://www.megamillions.com/cmspages/utilservice.asmx/GetLatestDrawData") else {
                throw LotterServiceError.invalidURL
            }

            let (data, _) = try await URLSession.shared.data(from: url)
            response = String(data: data, encoding: .utf8)
        }

        guard let json = response else {
            throw LotterServiceError.noData
        }

        do {
            // Parse the outer JSON response
            guard let jsonData = json.data(using: .utf8),
                  let outerJson = try JSONSerialization.jsonObject(with: jsonData) as? [String: Any],
                  let innerJsonString = outerJson["d"] as? String else {
                throw LotterServiceError.parsingError
            }

            // Parse the inner JSON string
            guard let innerJsonData = innerJsonString.data(using: .utf8),
                  let innerJson = try JSONSerialization.jsonObject(with: innerJsonData) as? [String: Any] else {
                throw LotterServiceError.parsingError
            }

            // Extract jackpot information
            guard let jackpot = innerJson["Jackpot"] as? [String: Any],
                  let nextPrizePool = jackpot["NextPrizePool"] as? Double,
                  let nextCashValue = jackpot["NextCashValue"] as? Double else {
                throw LotterServiceError.parsingError
            }

            // Extract next drawing date
            guard let nextDrawingDateString = innerJson["NextDrawingDate"] as? String else {
                throw LotterServiceError.parsingError
            }

            let drawingDate = parseDrawingDate(from: nextDrawingDateString)

            // Format the jackpot and cash value as strings
            let estimatedJackpot = formatCurrency(nextPrizePool)
            let cashValue = formatCurrency(nextCashValue)

            return DrawingInfo(date: drawingDate,
                               estimatedJackpot: estimatedJackpot,
                               cashValue: cashValue)

        } catch {
            throw LotterServiceError.parsingError
        }
    }

    private func parseDrawingDate(from dateString: String) -> Date {
        // Expected format: "2025-07-01T23:00:00"
        let formatter = ISO8601DateFormatter()
        formatter.formatOptions = [.withInternetDateTime]

        return formatter.date(from: dateString) ?? Date()
    }

    private func formatCurrency(_ amount: Double) -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = "USD"
        formatter.maximumFractionDigits = 0

        // Convert to millions for display
        let millions = amount / 1_000_000
        if millions >= 1000 {
            let billions = millions / 1000
            return String(format: "$%.1f Billion", billions)
        } else {
            return String(format: "$%.0f Million", millions)
        }
    }
}
