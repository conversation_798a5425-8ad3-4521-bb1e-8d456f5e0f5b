//
//  MegaMillionsService.swift
//  LotteryLightbox
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 7/1/25.
//

import Foundation

class MegaMillionsService: LotterServiceProtocol {
    var response: String?
    
    init(response: String? = nil) {
        self.response = response
    }
    
    func fetchNextDrawingInfo() async throws -> DrawingInfo {
        if response == nil {
            guard let url = URL(string: "https://www.megamillions.com/cmspages/utilservice.asmx/GetLatestDrawData") else {
                throw LotterServiceError.invalidURL
            }

            let (data, _) = try await URLSession.shared.data(from: url)
            response = String(data: data, encoding: .utf8)
        }
        
        guard let json = response else {
            throw LotterServiceError.noData
        }
        
        // parse json and fetch drawing info
    }
}
