//
//  LotteryDetailViewModel.swift
//  LotteryLightbox
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 6/30/25.
//

import Foundation
import SwiftUI

@MainActor
class LotteryDetailViewModel: ObservableObject {
    @Published var drawingInfo: DrawingInfo?
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let powerballService = PowerballService()
    private let megaMillionsService = MegaMillionsService()
    
    func fetchDrawingInfo(for lotteryType: LotteryType) {
        isLoading = true
        errorMessage = nil
        drawingInfo = nil
        
        Task {
            do {
                let info: DrawingInfo
                switch lotteryType {
                case .powerball:
                    info = try await powerballService.fetchNextDrawingInfo()
                case .megaMillions:
                    info = try await megaMillionsService.fetchNextDrawingInfo()
                }
                
                self.drawingInfo = info
                self.isLoading = false
            } catch {
                self.errorMessage = error.localizedDescription
                self.isLoading = false
            }
        }
    }
}

// MARK: - Preview Helper
struct LotteryDetailViewModelPreview: View {
    @StateObject private var viewModel = LotteryDetailViewModel()

    var body: some View {
        VStack(spacing: 20) {
            Text("ViewModel State Demo")
                .font(.title2)
                .fontWeight(.bold)

            Group {
                Text("Loading: \(viewModel.isLoading ? "Yes" : "No")")
                Text("Error: \(viewModel.errorMessage ?? "None")")
                Text("Data: \(viewModel.drawingInfo?.estimatedJackpot ?? "None")")
            }
            .font(.body)
            .padding(.horizontal)

            HStack(spacing: 20) {
                Button("Fetch Powerball") {
                    viewModel.fetchDrawingInfo(for: .powerball)
                }
                .buttonStyle(.borderedProminent)

                Button("Fetch Mega Millions") {
                    viewModel.fetchDrawingInfo(for: .megaMillions)
                }
                .buttonStyle(.bordered)
            }
        }
        .padding()
    }
}

#Preview("ViewModel Demo") {
    LotteryDetailViewModelPreview()
        .frame(width: 400, height: 300)
}
