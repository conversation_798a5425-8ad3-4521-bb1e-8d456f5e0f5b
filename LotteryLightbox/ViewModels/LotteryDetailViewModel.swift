//
//  LotteryDetailViewModel.swift
//  LotteryLightbox
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 6/30/25.
//

import Foundation

@MainActor
class LotteryDetailViewModel: ObservableObject {
    @Published var drawingInfo: DrawingInfo?
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let powerballService = PowerballService()
    private let megaMillionsService = MegaMillionsService()
    
    func fetchDrawingInfo(for lotteryType: LotteryType) {
        isLoading = true
        errorMessage = nil
        drawingInfo = nil
        
        Task {
            do {
                let info: DrawingInfo
                switch lotteryType {
                case .powerball:
                    info = try await powerballService.fetchNextDrawingInfo()
                case .megaMillions:
                    info = try await megaMillionsService.fetchNextDrawingInfo()
                }
                
                self.drawingInfo = info
                self.isLoading = false
            } catch {
                self.errorMessage = error.localizedDescription
                self.isLoading = false
            }
        }
    }
}
