//
//  MegaMillionsServiceTest.swift
//  LotteryLightboxTests
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 7/1/25.
//

import Foundation
import Testing
@testable import LotteryLightbox

class MegaMillionsServiceTest {
    @Test func testMegaMillionsServiceWithMockData() async throws {
        // Load JSON from file
        let testBundle = Bundle(for: type(of: self))
        guard let jsonPath = testBundle.path(forResource: "megamillions", ofType: "json"),
              let mockJSON = try? String(contentsOfFile: jsonPath, encoding: .utf8) else {
            throw NSError(domain: "TestError", code: 1, userInfo: [NSLocalizedDescriptionKey: "Could not load megamillions.json file"])
        }

        let service = MegaMillionsService(response: mockJSON)
        let drawingInfo = try await service.fetchNextDrawingInfo()

        // Verify the parsed data matches expected values from the JSON
        // NextPrizePool: 50000000.0 -> "$50 Million"
        // NextCashValue: 22400000.0 -> "$22 Million" (22.4 rounds to 22)
        #expect(drawingInfo.estimatedJackpot == "$50 Million")
        #expect(drawingInfo.cashValue == "$22 Million")
    }

    @Test func testMegaMillionsServiceErrorHandling() async throws {
        let invalidJSON = "{\"invalid\": \"json structure\"}"

        let service = MegaMillionsService(response: invalidJSON)

        await #expect(throws: LotterServiceError.parsingError) {
            try await service.fetchNextDrawingInfo()
        }
    }

    @Test func testMegaMillionsServiceWithMalformedInnerJSON() async throws {
        let malformedJSON = "{\"d\": \"not valid json\"}"

        let service = MegaMillionsService(response: malformedJSON)

        await #expect(throws: LotterServiceError.parsingError) {
            try await service.fetchNextDrawingInfo()
        }
    }

    @Test func testMegaMillionsServiceWithMissingFields() async throws {
        let incompleteJSON = "{\"d\": \"{\\\"Drawing\\\": {\\\"PlayDate\\\": \\\"2025-06-27T00:00:00\\\"}}\"}"

        let service = MegaMillionsService(response: incompleteJSON)

        await #expect(throws: LotterServiceError.parsingError) {
            try await service.fetchNextDrawingInfo()
        }
    }
}
